package com.insurfact.ins.api;

import com.insurfact.ins.dto.AdvisorProfileDTO;
import com.insurfact.ins.dto.ContactDTO;
import com.insurfact.ins.exception.ResourceNotFoundException;
import com.insurfact.ins.service.AdvisorService;
import com.insurfact.ins.security.PermissionService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AdvisorController.
 *
 * Tests focus on the actual business functionality using the existing test user ID 44444
 * from the database. This approach allows us to concentrate on testing the specific
 * business logic and API endpoints rather than spending time on user setup/teardown.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@ExtendWith(MockitoExtension.class)
class AdvisorControllerTest {

    // Test constants - using existing test user ID from database
    private static final Long TEST_USER_ID = 44444L;
    private static final Long TEST_ADVISOR_ID = 44444L; // Assuming advisor ID matches user ID for test data
    private static final String TEST_ADVISOR_CODE = "TEST001";
    private static final String TEST_USERNAME = "test.advisor";

    @Mock
    private AdvisorService advisorService;

    @InjectMocks
    private AdvisorController advisorController;

    private AdvisorProfileDTO testAdvisorProfile;
    private Authentication mockAuthentication;

    @BeforeEach
    void setUp() {
        // Create test advisor profile using existing test user ID 44444
        ContactDTO testContact = ContactDTO.builder()
            .contactId(TEST_USER_ID)
            .firstName("Test")
            .lastName("Advisor")
            .sin("***-***-444") // Masked SIN for test user
            .build();

        testAdvisorProfile = AdvisorProfileDTO.builder()
            .advisorId(TEST_ADVISOR_ID)
            .advisorCode(TEST_ADVISOR_CODE)
            .status("ACTIVE")
            .advisorType("AGENT")
            .createdDate(LocalDateTime.now())
            .hasLoginAccount(true)
            .username(TEST_USERNAME)
            .contact(testContact)
            .build();

        // Create mock authentication for test user
        mockAuthentication = new UsernamePasswordAuthenticationToken(
            TEST_USERNAME,
            null,
            Arrays.asList(new SimpleGrantedAuthority("ROLE_ADVISOR"))
        );
    }

    @Test
    void getAdvisorById_Success_WithTestUser() {
        // Given - Using existing test user ID 44444
        when(advisorService.getAdvisorProfile(TEST_ADVISOR_ID)).thenReturn(testAdvisorProfile);

        // When
        ResponseEntity<AdvisorProfileDTO> response = advisorController.getAdvisorById(TEST_ADVISOR_ID);

        // Then - Verify API response structure and business logic
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());

        AdvisorProfileDTO advisor = response.getBody();
        assertEquals(TEST_ADVISOR_ID, advisor.getAdvisorId());
        assertEquals(TEST_ADVISOR_CODE, advisor.getAdvisorCode());
        assertEquals("ACTIVE", advisor.getStatus());
        assertEquals("AGENT", advisor.getAdvisorType());
        assertTrue(advisor.getHasLoginAccount());
        assertEquals(TEST_USERNAME, advisor.getUsername());
        assertNotNull(advisor.getCreatedDate());

        verify(advisorService, times(1)).getAdvisorProfile(TEST_ADVISOR_ID);
    }

    @Test
    void getAdvisorById_NotFound_InvalidAdvisorId() {
        // Given - Test with non-existent advisor ID
        Long nonExistentAdvisorId = 99999L;
        when(advisorService.getAdvisorProfile(nonExistentAdvisorId))
            .thenThrow(new ResourceNotFoundException("Advisor", nonExistentAdvisorId));

        // When & Then - Verify proper exception handling
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            advisorController.getAdvisorById(nonExistentAdvisorId);
        });

        assertEquals("Advisor", exception.getResourceName());
        assertEquals(nonExistentAdvisorId, exception.getResourceId());
        verify(advisorService, times(1)).getAdvisorProfile(nonExistentAdvisorId);
    }

    @Test
    void getAdvisorById_InvalidId_NullParameter() {
        // Given - Test with null advisor ID
        Long invalidId = null;
        when(advisorService.getAdvisorProfile(invalidId))
            .thenThrow(new IllegalArgumentException("Advisor ID cannot be null"));

        // When & Then - Verify input validation
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            advisorController.getAdvisorById(invalidId);
        });

        assertEquals("Advisor ID cannot be null", exception.getMessage());
        verify(advisorService, times(1)).getAdvisorProfile(invalidId);
    }

    @Test
    void checkAdvisorExists_Success_WithTestUser() {
        // Given - Using existing test user ID 44444
        when(advisorService.advisorExists(TEST_ADVISOR_ID)).thenReturn(true);

        // When
        ResponseEntity<Void> response = advisorController.checkAdvisorExists(TEST_ADVISOR_ID);

        // Then - Verify successful existence check
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody()); // Void response should have no body
        verify(advisorService, times(1)).advisorExists(TEST_ADVISOR_ID);
    }

    @Test
    void checkAdvisorExists_NotFound_NonExistentAdvisor() {
        // Given - Test with non-existent advisor ID
        Long nonExistentAdvisorId = 99999L;
        when(advisorService.advisorExists(nonExistentAdvisorId)).thenReturn(false);

        // When
        ResponseEntity<Void> response = advisorController.checkAdvisorExists(nonExistentAdvisorId);

        // Then - Verify proper NOT_FOUND response
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody()); // Void response should have no body
        verify(advisorService, times(1)).advisorExists(nonExistentAdvisorId);
    }

    @Test
    void getAdvisorById_ContactInformation_BusinessLogicValidation() {
        // Given - Using test user with contact information
        when(advisorService.getAdvisorProfile(TEST_ADVISOR_ID)).thenReturn(testAdvisorProfile);

        // When
        ResponseEntity<AdvisorProfileDTO> response = advisorController.getAdvisorById(TEST_ADVISOR_ID);

        // Then - Verify contact information structure and business rules
        assertEquals(HttpStatus.OK, response.getStatusCode());
        AdvisorProfileDTO advisor = response.getBody();

        // Validate contact information is properly populated
        assertNotNull(advisor.getContact());
        ContactDTO contact = advisor.getContact();
        assertEquals(TEST_USER_ID, contact.getContactId());
        assertEquals("Test", contact.getFirstName());
        assertEquals("Advisor", contact.getLastName());
        assertEquals("***-***-444", contact.getSin()); // Verify SIN is properly masked for security

        verify(advisorService, times(1)).getAdvisorProfile(TEST_ADVISOR_ID);
    }

    @Test
    void getAdvisorById_LoginAccountInfo_BusinessLogicValidation() {
        // Given - Using test user with login account
        when(advisorService.getAdvisorProfile(TEST_ADVISOR_ID)).thenReturn(testAdvisorProfile);

        // When
        ResponseEntity<AdvisorProfileDTO> response = advisorController.getAdvisorById(TEST_ADVISOR_ID);

        // Then - Verify login account information and business rules
        assertEquals(HttpStatus.OK, response.getStatusCode());
        AdvisorProfileDTO advisor = response.getBody();

        // Validate login account information
        assertTrue(advisor.getHasLoginAccount());
        assertEquals(TEST_USERNAME, advisor.getUsername());
        assertNotNull(advisor.getUsername()); // Username should never be null for accounts with login

        verify(advisorService, times(1)).getAdvisorProfile(TEST_ADVISOR_ID);
    }

    @Test
    void getAdvisorById_DataTransformation_VerifyDTOMapping() {
        // Given - Test the complete data transformation from entity to DTO
        when(advisorService.getAdvisorProfile(TEST_ADVISOR_ID)).thenReturn(testAdvisorProfile);

        // When
        ResponseEntity<AdvisorProfileDTO> response = advisorController.getAdvisorById(TEST_ADVISOR_ID);

        // Then - Verify all DTO fields are properly mapped and transformed
        assertEquals(HttpStatus.OK, response.getStatusCode());
        AdvisorProfileDTO advisor = response.getBody();

        // Verify core advisor fields
        assertNotNull(advisor);
        assertEquals(TEST_ADVISOR_ID, advisor.getAdvisorId());
        assertEquals(TEST_ADVISOR_CODE, advisor.getAdvisorCode());
        assertEquals("ACTIVE", advisor.getStatus());
        assertEquals("AGENT", advisor.getAdvisorType());

        // Verify nested contact object is properly mapped
        assertNotNull(advisor.getContact());

        // Verify timestamp fields are handled correctly
        assertNotNull(advisor.getCreatedDate());

        verify(advisorService, times(1)).getAdvisorProfile(TEST_ADVISOR_ID);
    }
}
